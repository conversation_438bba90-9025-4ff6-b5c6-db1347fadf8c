"use client"

import { useEffect, useRef, useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import newsData from "@/data/news.json"
import { generateSlug } from "@/lib/utils"

export default function NewsSection() {
  const sectionRef = useRef<HTMLElement>(null)
  const [news] = useState(newsData.slice(0, 3))

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1 },
    )

    const elements = sectionRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  return (
    <section ref={sectionRef} className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16 animate-on-scroll">
          <h2 className="text-4xl font-bold text-gray-900 mb-4 animate-on-scroll">News & Media</h2>
          <p className="text-xl text-gray-600 animate-on-scroll animation-delay-100">
            Stay updated with our latest insights and achievements
          </p>
        </div>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {news.map((article, index) => (
            <div
              key={article.id}
              className={`bg-white rounded-lg shadow-lg overflow-hidden animate-on-scroll animation-delay-${(index + 1) * 100}`}
            >
              <Image
                src={article.image || "/placeholder.svg"}
                alt={article.title}
                width={400}
                height={250}
                className="w-full h-48 object-cover animate-on-scroll animation-delay-${(index + 1) * 150}"
              />
              <div className="p-6">
                <div className="text-sm text-brand-primary mb-2 animate-on-scroll animation-delay-${(index + 1) * 200}">
                  {article.category}
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3 animate-on-scroll animation-delay-${(index + 1) * 250}">
                  {article.title}
                </h3>
                <p className="text-gray-600 mb-4 line-clamp-3 animate-on-scroll animation-delay-${(index + 1) * 300}">
                  {article.description}
                </p>
                <div className="flex justify-between items-center animate-on-scroll animation-delay-${(index + 1) * 350}">
                  <span className="text-sm text-gray-500">{new Date(article.date).toLocaleDateString()}</span>
                  <Link href={`/news/${generateSlug(article.title)}`}>
                    <Button variant="outline" size="sm">
                      Read More
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
        <div className="text-center animate-on-scroll animation-delay-400">
          <Link href="/news">
            <Button size="lg" className="bg-brand-primary hover:bg-brand-accent text-white">
              View More News
            </Button>
          </Link>
        </div>
      </div>
    </section>
  )
}
