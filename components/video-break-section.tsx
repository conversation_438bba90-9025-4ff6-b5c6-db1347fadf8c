"use client"

import { useState, useRef, useEffect } from "react"
import { Play, Pause, Volume2, VolumeX } from "lucide-react"
import { Button } from "@/components/ui/button"

export default function VideoBreakSection() {
  const [isPlaying, setIsPlaying] = useState(false)
  const [isMuted, setIsMuted] = useState(true)
  const [showControls, setShowControls] = useState(false)
  const videoRef = useRef<HTMLVideoElement>(null)
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1 },
    )

    const elements = sectionRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  useEffect(() => {
    const video = videoRef.current
    if (video) {
      // Auto-play the video when component mounts
      video
        .play()
        .then(() => {
          setIsPlaying(true)
        })
        .catch(() => {
          // Auto-play failed, user interaction required
          setIsPlaying(false)
        })
    }
  }, [])

  const togglePlay = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause()
      } else {
        videoRef.current.play()
      }
      setIsPlaying(!isPlaying)
    }
  }

  const toggleMute = () => {
    if (videoRef.current) {
      videoRef.current.muted = !isMuted
      setIsMuted(!isMuted)
    }
  }

  const handleVideoClick = () => {
    togglePlay()
  }

  return (
    <section ref={sectionRef} className="relative py-20 bg-gray-900 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-gradient-to-r from-brand-primary/20 to-brand-secondary/20" />
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: `radial-gradient(circle at 25% 25%, rgba(215, 112, 71, 0.1) 0%, transparent 50%),
                           radial-gradient(circle at 75% 75%, rgba(224, 138, 95, 0.1) 0%, transparent 50%)`,
          }}
        />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="text-center mb-12 animate-on-scroll">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Experience Our <span className="text-brand-secondary">Creative Process</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Watch how we transform ideas into architectural masterpieces through our innovative design process
          </p>
        </div>

        <div className="max-w-5xl mx-auto animate-on-scroll animation-delay-200">
          <div
            className="relative rounded-2xl overflow-hidden shadow-2xl cursor-pointer group"
            onMouseEnter={() => setShowControls(true)}
            onMouseLeave={() => setShowControls(false)}
            onClick={handleVideoClick}
          >
            {/* Video Element */}
            <video
              ref={videoRef}
              className="w-full h-[400px] md:h-[500px] lg:h-[600px] object-cover"
              muted={isMuted}
              loop
              autoPlay
              playsInline
              poster="/placeholder.svg?height=600&width=1000&text=Video+Poster"
            >
              <source src="/img/photo/carve-art2.mp4" type="video/mp4" />
              {/* Fallback for browsers that don't support video */}
              <div className="w-full h-[400px] md:h-[500px] lg:h-[600px] bg-gradient-to-br from-brand-primary to-brand-secondary flex items-center justify-center">
                <div className="text-center text-white">
                  <Play className="h-20 w-20 mx-auto mb-4 opacity-80" />
                  <p className="text-xl">Your browser doesn't support video playback</p>
                </div>
              </div>
            </video>

            {/* Video Overlay */}
            <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-all duration-300" />

            {/* Play/Pause Button */}
            <div
              className={`absolute inset-0 flex items-center justify-center transition-opacity duration-300 ${
                isPlaying && !showControls ? "opacity-0" : "opacity-100"
              }`}
            >
              <Button
                size="lg"
                className="bg-white/90 hover:bg-white text-brand-primary rounded-full p-6 shadow-xl transform hover:scale-110 transition-all duration-300"
                onClick={(e) => {
                  e.stopPropagation()
                  togglePlay()
                }}
              >
                {isPlaying ? <Pause className="h-8 w-8" /> : <Play className="h-8 w-8 ml-1" />}
              </Button>
            </div>

            {/* Video Controls */}
            <div
              className={`absolute bottom-6 right-6 flex space-x-3 transition-opacity duration-300 ${
                showControls ? "opacity-100" : "opacity-0"
              }`}
            >
              <Button
                size="sm"
                variant="secondary"
                className="bg-black/50 hover:bg-black/70 text-white border-none rounded-full p-3"
                onClick={(e) => {
                  e.stopPropagation()
                  toggleMute()
                }}
              >
                {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
              </Button>
            </div>

            {/* Video Title Overlay */}
            <div className="absolute bottom-6 left-6">
              <div className="bg-black/50 backdrop-blur-sm rounded-lg px-4 py-2">
                <h3 className="text-white font-semibold text-lg">CARVE Atelier Design Process</h3>
                <p className="text-gray-300 text-sm">From concept to completion</p>
              </div>
            </div>
          </div>

          {/* Video Description */}
          <div className="mt-8 text-center animate-on-scroll animation-delay-400">
            <p className="text-gray-400 max-w-2xl mx-auto leading-relaxed">
              This exclusive behind-the-scenes look showcases our meticulous approach to architectural design, from
              initial sketches to final construction. See how we bring innovative concepts to life.
            </p>
          </div>
        </div>

        {/* Decorative Elements */}
        <div className="absolute top-20 left-10 w-20 h-20 border border-brand-primary/30 rounded-full animate-pulse" />
        <div className="absolute bottom-20 right-10 w-16 h-16 border border-brand-secondary/30 rounded-full animate-pulse animation-delay-1000" />
        <div className="absolute top-1/2 left-20 w-2 h-2 bg-brand-primary rounded-full animate-ping animation-delay-2000" />
        <div className="absolute top-1/3 right-20 w-2 h-2 bg-brand-secondary rounded-full animate-ping animation-delay-3000" />
      </div>
    </section>
  )
}
