"use client"

import { useEffect, useRef } from "react"
import Image from "next/image"

export default function UnveilingSection() {
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1 },
    )

    const elements = sectionRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  return (
    <section ref={sectionRef} className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="animate-on-scroll animation-delay-200">
            <h2 className="text-4xl font-bold mb-6 header-stylish font-sinkin">
              <span className="text-primary-part">Unveiling the Potential</span> <span className="text-brand-part">in Every Situation</span>
            </h2>
            <div className="space-y-4 text-lg text-gray-600 leading-relaxed font-exo">
              <p>
                Just as CARVE means to artfully craft out beautiful desired outcomes out of basic and crude items, we at
                CARVE ATELIER are driven by the vision of gracefully carving out creative architectural design &
                solutions out of fallow situations or complex challenges.
              </p>
              <p>
                With the quality of craft that carving entails, we sculpt every detail with style and precision powered
                by rigorous research, innovative design processes and a clear focus on the goals of our clients.
              </p>
              <p className="font-semibold text-brand-primary">
                We love to bring out the best out of everything
                <br />
                We love Carving
                <br />
                We are CARVE ATELIER
              </p>
              <p className="text-sm text-gray-500 italic">
                atelier : [Translation] - a workshop or studio, especially one used by an artist or designer
              </p>
            </div>
          </div>
          <div className="animate-on-scroll">
            <div className="relative">
              <Image
                src="/img/customers/KM 46 MALL - PROJECT MEETING - SEPT 2ND - 05.jpg"
                alt="Client Collaboration Session"
                width={600}
                height={400}
                className="rounded-lg shadow-lg h-[550px] object-cover"
              />
              <div className="absolute bottom-4 left-4 bg-black/70 backdrop-blur-sm text-white px-4 py-2 rounded-lg">
                <p className="text-sm font-medium font-exo">Collaborative Design Session - KM 46 Mall Project</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
