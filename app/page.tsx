import type { Metadata } from "next"
import HomePage from "@/components/home-page"

export const metadata: Metadata = {
  title: "CARVE Atelier - Premier Architecture & Design Firm in Lagos, Nigeria",
  description:
    "CARVE Atelier is a leading architecture and design firm in Lagos, Nigeria, specializing in innovative commercial, residential, retail, and urban planning solutions. We craft spaces that inspire and transform communities.",
  keywords: [
    "architecture firm Lagos",
    "Nigerian architects",
    "commercial architecture Nigeria",
    "residential design Lagos",
    "retail architecture",
    "urban planning Nigeria",
    "sustainable architecture",
    "CARVE Atelier",
    "architectural design services",
    "building design Nigeria",
    "master planning Lagos",
    "innovative architecture",
  ],
  authors: [{ name: "CARVE Atelier" }],
  creator: "CARVE Atelier",
  publisher: "CARVE Atelier",
  robots: "index, follow",
  openGraph: {
    type: "website",
    locale: "en_NG",
    url: "https://carveatelier.com",
    siteName: "CARVE Atelier",
    title: "CARVE Atelier - Premier Architecture & Design Firm",
    description:
      "Leading architecture and design firm in Lagos, Nigeria. We create innovative spaces that inspire and transform communities.",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "CARVE Atelier - Architecture & Design",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "CARVE Atelier - Premier Architecture & Design Firm",
    description: "Leading architecture and design firm in Lagos, Nigeria. We create innovative spaces that inspire.",
    images: ["/twitter-image.jpg"],
  },
  alternates: {
    canonical: "https://carveatelier.com",
  },
}

export default function Home() {
  return <HomePage />
}
