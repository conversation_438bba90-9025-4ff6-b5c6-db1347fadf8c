"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Home, ArrowLeft, Search, Mail } from "lucide-react"

export default function NotFound() {
  const [mounted, setMounted] = useState(false)
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })

  useEffect(() => {
    setMounted(true)

    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY })
    }

    window.addEventListener('mousemove', handleMouseMove)
    return () => window.removeEventListener('mousemove', handleMouseMove)
  }, [])

  if (!mounted) return null

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 flex items-center justify-center relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-brand-primary/5 rounded-full animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-brand-secondary/5 rounded-full animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-brand-primary/3 rounded-full animate-ping delay-2000"></div>

        {/* Interactive cursor follower */}
        <div
          className="absolute w-4 h-4 bg-brand-primary/20 rounded-full pointer-events-none transition-all duration-300 ease-out"
          style={{
            left: mousePosition.x - 8,
            top: mousePosition.y - 8,
            transform: 'scale(1)',
          }}
        ></div>
        <div
          className="absolute w-8 h-8 border border-brand-secondary/30 rounded-full pointer-events-none transition-all duration-500 ease-out"
          style={{
            left: mousePosition.x - 16,
            top: mousePosition.y - 16,
          }}
        ></div>
      </div>

      <div className="relative z-10 text-center px-6 max-w-4xl mx-auto">
        {/* Logo */}
        <div className="mb-8 animate-fade-in-up">
          <Image
            src="/img/logo/logo-light.png"
            alt="CARVE Atelier"
            width={200}
            height={80}
            className="mx-auto h-16 w-auto opacity-80"
          />
        </div>

        {/* 404 Number */}
        <div className="mb-8 animate-fade-in-up animation-delay-200">
          <h1 className="text-9xl md:text-[12rem] font-bold text-transparent bg-clip-text bg-gradient-to-r from-brand-primary to-brand-secondary leading-none">
            404
          </h1>
        </div>

        {/* Main Message */}
        <div className="mb-8 animate-fade-in-up animation-delay-400">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Oops! Page Not Found
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
            The architectural masterpiece you're looking for seems to have been relocated. 
            Don't worry, even the best architects sometimes need to revise their blueprints.
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12 animate-fade-in-up animation-delay-600">
          <Link href="/">
            <Button size="lg" className="bg-brand-primary hover:bg-brand-primary/90 text-white px-8 py-3 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
              <Home className="mr-2 h-5 w-5" />
              Back to Home
            </Button>
          </Link>
          
          <Link href="/work">
            <Button variant="outline" size="lg" className="border-brand-primary text-brand-primary hover:bg-brand-primary hover:text-white px-8 py-3 rounded-full transition-all duration-300 transform hover:scale-105">
              <Search className="mr-2 h-5 w-5" />
              Explore Projects
            </Button>
          </Link>
        </div>

        {/* Quick Links */}
        <div className="animate-fade-in-up animation-delay-800">
          <p className="text-sm text-gray-500 mb-4">Or explore these popular sections:</p>
          <div className="flex flex-wrap justify-center gap-4 text-sm">
            <Link href="/about" className="text-brand-primary hover:text-brand-secondary transition-colors duration-200 hover:underline">
              About Us
            </Link>
            <span className="text-gray-300">•</span>
            <Link href="/work" className="text-brand-primary hover:text-brand-secondary transition-colors duration-200 hover:underline">
              Our Work
            </Link>
            <span className="text-gray-300">•</span>
            <Link href="/news" className="text-brand-primary hover:text-brand-secondary transition-colors duration-200 hover:underline">
              News & Media
            </Link>
            <span className="text-gray-300">•</span>
            <Link href="/contact" className="text-brand-primary hover:text-brand-secondary transition-colors duration-200 hover:underline">
              Contact
            </Link>
          </div>
        </div>

        {/* Architectural Quote */}
        <div className="mt-8 animate-fade-in-up animation-delay-900">
          <blockquote className="text-gray-600 italic text-sm max-w-md mx-auto">
            "Architecture is a visual art, and the buildings speak for themselves."
            <footer className="text-gray-400 text-xs mt-2">— Julia Morgan</footer>
          </blockquote>
        </div>

        {/* Contact Info */}
        <div className="mt-12 pt-8 border-t border-gray-200 animate-fade-in-up animation-delay-1000">
          <p className="text-gray-500 text-sm mb-2">Need help? Get in touch with us</p>
          <Link href="/contact" className="inline-flex items-center text-brand-primary hover:text-brand-secondary transition-colors duration-200">
            <Mail className="mr-2 h-4 w-4" />
            <EMAIL>
          </Link>
        </div>
      </div>
    </div>
  )
}
