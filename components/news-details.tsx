"use client"

import { useState, useEffect, useRef } from "react"
import Image from "next/image"
import Link from "next/link"
import { ArrowLeft, Calendar, Tag } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import newsData from "@/data/news.json"
import { findNewsBySlug, generateSlug } from "@/lib/utils"

interface NewsDetailsProps {
  newsSlug: string
}

export default function NewsDetails({ newsSlug }: NewsDetailsProps) {
  const [article, setArticle] = useState<any>(null)
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const foundArticle = findNewsBySlug(newsData, newsSlug)
    setArticle(foundArticle)
  }, [newsSlug])

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1 },
    )

    const elements = sectionRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [article])

  if (!article) {
    return <div className="py-20 text-center">Article not found</div>
  }

  return (
    <section ref={sectionRef} className="py-20 bg-white">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8 animate-on-scroll">
          <Link href="/news">
            <Button variant="outline" className="mb-6">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to News
            </Button>
          </Link>
        </div>

        {/* Article Header */}
        <div className="mb-8 animate-on-scroll">
          <div className="flex items-center space-x-4 mb-4">
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <Tag className="h-4 w-4" />
              <span>{article.category}</span>
            </div>
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <Calendar className="h-4 w-4" />
              <span>{new Date(article.date).toLocaleDateString()}</span>
            </div>
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">{article.title}</h1>
          <p className="text-xl text-gray-600 leading-relaxed">{article.description}</p>
        </div>

        {/* Featured Image */}
        <div className="mb-8 animate-on-scroll">
          <Image
            src={article.image || "/placeholder.svg"}
            alt={article.title}
            width={800}
            height={400}
            className="w-full h-96 object-cover rounded-lg shadow-lg"
          />
        </div>

        {/* Article Content */}
        <div className="prose prose-lg max-w-none animate-on-scroll">
          {article.content.split("\n\n").map((paragraph: string, index: number) => {
            if (paragraph.startsWith("**") && paragraph.endsWith("**")) {
              return (
                <h3 key={index} className="text-2xl font-bold text-gray-900 mt-8 mb-4">
                  {paragraph.replace(/\*\*/g, "")}
                </h3>
              )
            }
            return (
              <p key={index} className="text-gray-600 leading-relaxed mb-6">
                {paragraph}
              </p>
            )
          })}
        </div>

        {/* Related Articles */}
        {newsData
              .filter((a) => a.id !== article.id && a.category === article.category)
              .slice(0, 2)
              .map((relatedArticle) => (
        <div className="mt-16 pt-8 border-t animate-on-scroll">
          <h3 className="text-2xl font-bold text-gray-900 mb-6">Related Articles</h3>
          <div className="grid md:grid-cols-2 gap-6">
            
                <Link key={relatedArticle.id} href={`/news/${generateSlug(relatedArticle.title)}`}>
                  <div className="bg-gray-50 rounded-lg p-6 hover:bg-gray-100 transition-colors">
                    <h4 className="font-semibold text-gray-900 mb-2">{relatedArticle.title}</h4>
                    <p className="text-gray-600 text-sm line-clamp-2">{relatedArticle.description}</p>
                    <span className="text-brand-primary text-sm font-medium mt-2 inline-block">Read more →</span>
                  </div>
                </Link>
              
          </div>
        </div>
        ))}
      </div>
    </section>
  )
}
