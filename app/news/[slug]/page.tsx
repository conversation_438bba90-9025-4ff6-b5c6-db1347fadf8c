import type { Metadata } from "next"
import Navbar from "@/components/navbar"
import Footer from "@/components/footer"
import NewsDetails from "@/components/news-details"
import PageBanner from "@/components/page-banner"
import newsData from "@/data/news.json"
import { findNewsBySlug } from "@/lib/utils"

type Props = {
  params: Promise<{ slug: string }>
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { slug } = await params
  const article = findNewsBySlug(newsData, slug)

  if (!article) {
    return {
      title: "Article Not Found - CARVE Atelier",
      description: "The requested article could not be found.",
    }
  }

  return {
    title: `${article.title} - CARVE Atelier News`,
    description: article.description,
    keywords: [
      article.title,
      article.category,
      "CARVE Atelier news",
      "architecture news Nigeria",
      "design insights",
      "building industry",
      "architectural achievements",
    ],
    openGraph: {
      title: article.title,
      description: article.description,
      url: `https://carveatelier.com/news/${slug}`,
      images: [
        {
          url: article.image || "/og-news.jpg",
          width: 1200,
          height: 630,
          alt: article.title,
        },
      ],
    },
    alternates: {
      canonical: `https://carveatelier.com/news/${slug}`,
    },
  }
}

export default async function NewsPage({ params }: { params: Promise<{ slug: string }> }) {
  const { slug } = await params
  const article = findNewsBySlug(newsData, slug)

  if (!article) {
    return (
      <main className="min-h-screen">
        <Navbar />
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">Article Not Found</h1>
            <p className="text-gray-600">The article you're looking for doesn't exist.</p>
          </div>
        </div>
        <Footer />
      </main>
    )
  }

  return (
    <main className="min-h-screen">
      <Navbar />
      <PageBanner
        title={article.title}
        subtitle="Insights and updates from the world of architecture"
        backgroundImage={article.image || "/placeholder.svg?height=600&width=1200"}
        breadcrumbs={[{ label: "Home", href: "/" }, { label: "News & Media", href: "/news" }, { label: article.title }]}
      />
      <NewsDetails newsSlug={slug} />
      <Footer />
    </main>
  )
}
