"use client"

import { useEffect, useRef } from "react"
import Image from "next/image"

const logos = [
  "/placeholder.svg?height=80&width=120",
  "/placeholder.svg?height=80&width=120",
  "/placeholder.svg?height=80&width=120",
  "/placeholder.svg?height=80&width=120",
  "/placeholder.svg?height=80&width=120",
  "/placeholder.svg?height=80&width=120",
]

export default function CompanyLogosSection() {
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1 },
    )

    const elements = sectionRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  return (
    <section ref={sectionRef} className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16 animate-on-scroll">
          <h2 className="text-4xl font-bold mb-4 header-stylish font-sinkin">
            <span className="text-primary-part">Our</span> <span className="text-brand-part">Partners</span>
          </h2>
          <p className="text-xl text-gray-600 font-exo">Trusted by leading organizations</p>
        </div>
        <div className="animate-on-scroll">
          <div className="flex overflow-hidden">
            <div className="flex animate-scroll">
              {[...logos, ...logos].map((logo, index) => (
                <div key={index} className="flex-shrink-0 mx-8">
                  <Image
                    src={logo || "/placeholder.svg"}
                    alt={`Partner ${index + 1}`}
                    width={120}
                    height={80}
                    className="h-16 w-auto object-contain opacity-60 hover:opacity-100 transition-opacity"
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
