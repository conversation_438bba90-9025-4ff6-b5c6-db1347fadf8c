import type { Metadata } from "next"
import Navbar from "@/components/navbar"
import Footer from "@/components/footer"
import NewsGrid from "@/components/news-grid"
import PageBanner from "@/components/page-banner"

export const metadata: Metadata = {
  title: "News & Media - CARVE Atelier | Architecture Insights & Updates",
  description:
    "Stay updated with the latest news, insights, and achievements from CARVE Atelier. Read about our architectural projects, industry trends, awards, and thought leadership in Nigerian architecture.",
  keywords: [
    "CARVE Atelier news",
    "architecture news Nigeria",
    "design insights Lagos",
    "architectural awards",
    "building industry news",
    "sustainable design trends",
    "architecture blog Nigeria",
    "design philosophy",
    "construction news Lagos",
    "architectural achievements",
  ],
  openGraph: {
    title: "News & Media - CARVE Atelier Architecture Insights",
    description:
      "Stay updated with the latest news, insights, and achievements from CARVE Atelier's architectural projects and industry leadership.",
    url: "https://carveatelier.com/news",
    images: [
      {
        url: "/og-news.jpg",
        width: 1200,
        height: 630,
        alt: "CARVE Atelier News & Media",
      },
    ],
  },
  alternates: {
    canonical: "https://carveatelier.com/news",
  },
}

export default function News() {
  return (
    <main className="min-h-screen">
      <Navbar />
      <PageBanner
        title="News & Media"
        subtitle="Stay updated with our latest insights and achievements"
        backgroundImage="/img/covers/blog1.jpg"
        breadcrumbs={[{ label: "Home", href: "/" }, { label: "News & Media" }]}
      />
      <NewsGrid />
      <Footer />
    </main>
  )
}
