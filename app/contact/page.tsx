import type { Metadata } from "next"
import Navbar from "@/components/navbar"
import Footer from "@/components/footer"
import ContactSection from "@/components/contact-section"
import MapSection from "@/components/map-section"
import PageBanner from "@/components/page-banner"

export const metadata: Metadata = {
  title: "Contact Us - CARVE Atelier | Get in Touch for Architecture Services",
  description:
    "Contact CARVE Atelier for your architectural and design needs in Lagos, Nigeria. Get in touch with our expert team for commercial, residential, retail, and urban planning projects. Located in Yaba, Lagos.",
  keywords: [
    "contact CARVE Atelier",
    "architecture firm Lagos contact",
    "architectural consultation Nigeria",
    "design services Lagos",
    "building design consultation",
    "architecture office Yaba",
    "contact architects Lagos",
    "architectural services inquiry",
    "design consultation Nigeria",
    "CARVE Atelier address",
  ],
  openGraph: {
    title: "Contact CARVE Atelier - Architecture & Design Services",
    description:
      "Get in touch with CARVE Atelier for expert architectural and design services in Lagos, Nigeria. Let's discuss your next project.",
    url: "https://carveatelier.com/contact",
    images: [
      {
        url: "/og-contact.jpg",
        width: 1200,
        height: 630,
        alt: "Contact CARVE Atelier",
      },
    ],
  },
  alternates: {
    canonical: "https://carveatelier.com/contact",
  },
}

export default function Contact() {
  return (
    <main className="min-h-screen">
      <Navbar />
      <PageBanner
        title="Contact Us"
        subtitle="Let's discuss your next architectural project"
        backgroundImage="/img/photo/2.webp"
      />
      <ContactSection />
      <MapSection />
      <Footer />
    </main>
  )
}
