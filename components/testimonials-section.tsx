"use client"

import { useState, useEffect, useRef } from "react"
import { <PERSON>uo<PERSON>, ChevronLeft, ChevronRight } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Image from "next/image"

const testimonials = [
  {
    name: "<PERSON> Virtue",
    role: "Property Developer",
    company: "Virtue Holdings",
    content:
      "Working with CARVE Atelier was an exceptional experience. Their team understood our vision perfectly and delivered a commercial space that exceeded our expectations. The attention to detail and innovative design approach made all the difference.",
    rating: 5,
  },
  {
    name: "<PERSON>",
    role: "Retail Manager",
    company: "Miller & Associates",
    content:
      "CARVE Atelier transformed our retail space into something truly spectacular. Their understanding of customer flow and brand identity resulted in a 40% increase in foot traffic. Highly recommended for any retail project.",
    rating: 5,
  },
  {
    name: "<PERSON>",
    role: "Homeowner",
    company: "Private Client",
    content:
      "Our dream home became a reality thanks to CARVE Atelier. They listened to our needs, incorporated our lifestyle requirements, and created a space that perfectly balances functionality with aesthetic appeal.",
    rating: 5,
  },
  {
    name: "<PERSON>",
    role: "Urban Planner",
    company: "City Development Corp",
    content:
      "The master planning project delivered by CARVE Atelier exceeded all expectations. Their innovative approach to urban design and community integration has set a new standard for sustainable development in our region.",
    rating: 5,
  },
]

export default function TestimonialsSection() {
  const [currentTestimonial, setCurrentTestimonial] = useState(0)
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1 },
    )

    const elements = sectionRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length)
    }, 8000)
    return () => clearInterval(timer)
  }, [])

  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => (prev + 1) % testimonials.length)
  }

  const prevTestimonial = () => {
    setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length)
  }

  const goToTestimonial = (index: number) => {
    setCurrentTestimonial(index)
  }

  return (
    <section ref={sectionRef} className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16 animate-on-scroll">
          <h2 className="text-4xl font-bold mb-4 header-stylish font-sinkin">
            <span className="text-primary-part">What Our Clients</span> <span className="text-brand-part">Say</span>
          </h2>
          <p className="text-xl text-gray-600 font-exo">Trusted by leading organizations and individuals</p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Image with Caption */}
          <div className="animate-on-scroll">
            <div className="relative">
              <Image
                src="/img/photo/office8.webp"
                alt="Client Testimonials"
                width={600}
                height={500}
                className="rounded-lg shadow-lg h-[500px] object-cover"
              />
              <div className="absolute bottom-4 left-4 bg-black/70 backdrop-blur-sm text-white px-4 py-2 rounded-lg">
                <p className="text-sm font-medium font-exo">Happy Clients - CARVE Atelier</p>
              </div>
            </div>
          </div>

          {/* Testimonials Content */}
          <div className="animate-on-scroll animation-delay-200">
            <div className="bg-white rounded-2xl shadow-xl p-8 md:p-10">
              <Quote className="h-12 w-12 text-brand-primary mb-6" />

              <div className="mb-8">
                <p className="text-xl md:text-2xl text-gray-700 leading-relaxed italic mb-6 font-exo">
                  "{testimonials[currentTestimonial].content}"
                </p>

                {/* Star Rating */}
                <div className="flex mb-6">
                  {[...Array(testimonials[currentTestimonial].rating)].map((_, i) => (
                    <svg key={i} className="w-5 h-5 text-yellow-400 fill-current" viewBox="0 0 20 20">
                      <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z" />
                    </svg>
                  ))}
                </div>
              </div>

              {/* Client Info */}
              <div className="border-t pt-6">
                <div className="flex items-center">
                  <div className="w-16 h-16 bg-brand-light rounded-full flex items-center justify-center mr-4">
                    <span className="text-brand-primary font-bold text-xl font-sinkin">
                      {testimonials[currentTestimonial].name
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </span>
                  </div>
                  <div className="text-left">
                    <h4 className="font-semibold text-gray-900 font-sinkin">{testimonials[currentTestimonial].name}</h4>
                    <p className="text-gray-600 text-sm font-exo">
                      {testimonials[currentTestimonial].role} at {testimonials[currentTestimonial].company}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Controls */}
        <div className="flex justify-center items-center mt-8 space-x-4">
          <Button
            variant="ghost"
            size="icon"
            className="bg-white shadow-lg hover:bg-gray-50 text-gray-600"
            onClick={prevTestimonial}
          >
            <ChevronLeft className="h-6 w-6" />
          </Button>
          
          <div className="flex space-x-2">
            {testimonials.map((_, index) => (
              <button
                key={index}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentTestimonial ? "bg-brand-primary" : "bg-gray-300"
                }`}
                onClick={() => goToTestimonial(index)}
              />
            ))}
          </div>

          <Button
            variant="ghost"
            size="icon"
            className="bg-white shadow-lg hover:bg-gray-50 text-gray-600"
            onClick={nextTestimonial}
          >
            <ChevronRight className="h-6 w-6" />
          </Button>
        </div>
      </div>
    </section>
  )
}
