"use client"

import { useState, useEffect, useRef } from "react"
import Image from "next/image"
import Link from "next/link"
import { ArrowLeft, MapPin, Calendar, User, Building } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import projectsData from "@/data/projects.json"
import { findProjectBySlug, generateSlug } from "@/lib/utils"

interface ProjectDetailsProps {
  projectSlug: string
}

export default function ProjectDetails({ projectSlug }: ProjectDetailsProps) {
  const sectionRef = useRef<HTMLElement>(null)

  // Find project directly instead of using state
  const project = findProjectBySlug(projectsData, projectSlug)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.remove("opacity-0", "translate-y-8", "animate-on-scroll")
            entry.target.classList.add("opacity-100", "translate-y-0", "animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1, rootMargin: "0px 0px -50px 0px" },
    )

    const elements = sectionRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [project])

  if (!project) {
    return <div className="py-20 text-center">Project not found</div>
  }

  return (
    <section ref={sectionRef} className="bg-white">
      {/* Container for text content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20">
        <div className="mb-8 animate-on-scroll opacity-0 translate-y-8 transition-all duration-1000 ease-out">
          <Link href="/work">
            <Button variant="outline" className="mb-6 font-sinkin">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Projects
            </Button>
          </Link>
        </div>

        {/* Project Title */}
        <div className="mb-12 animate-on-scroll opacity-0 translate-y-8 transition-all duration-1000 ease-out delay-200">
          <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-4 header-stylish font-sinkin">
            <span className="text-primary-part">{project.title.split(' ').slice(0, -1).join(' ')}</span>{" "}
            <span className="text-brand-part">{project.title.split(' ').slice(-1)[0]}</span>
          </h1>
        </div>
      </div>

      {/* Full Width Main Image - More than 100vh */}
      <div className="mb-12 animate-on-scroll opacity-0 translate-y-8 transition-all duration-1000 ease-out delay-400">
        <Image
          src={project.image || "/placeholder.svg"}
          alt={project.title}
          width={1600}
          height={1200}
          className="w-full h-[120vh] object-cover"
        />
      </div>

      {/* Container for remaining content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20">

        {/* Project Info - After Image */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12 animate-on-scroll opacity-0 translate-y-8 transition-all duration-1000 ease-out delay-600">
          <div className="flex items-center space-x-3">
            <Building className="h-5 w-5 text-brand-primary" />
            <div>
              <p className="text-sm text-gray-500 font-exo">Project Type</p>
              <p className="font-semibold font-sinkin">{project.type}</p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <MapPin className="h-5 w-5 text-brand-primary" />
            <div>
              <p className="text-sm text-gray-500 font-exo">Location</p>
              <p className="font-semibold font-sinkin">{project.location}</p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <Calendar className="h-5 w-5 text-brand-primary" />
            <div>
              <p className="text-sm text-gray-500 font-exo">Status</p>
              <p className="font-semibold font-sinkin">{project.status}</p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <User className="h-5 w-5 text-brand-primary" />
            <div>
              <p className="text-sm text-gray-500 font-exo">Client</p>
              <p className="font-semibold font-sinkin">{project.client}</p>
            </div>
          </div>
        </div>

        {/* Description - Full Width Paragraphs */}
        <div className="mb-12 animate-on-scroll opacity-0 translate-y-8 transition-all duration-1000 ease-out delay-800">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-8 header-stylish font-sinkin">
              <span className="text-primary-part">Project</span> <span className="text-brand-part">Overview</span>
            </h2>
            <div className="space-y-6 text-lg text-gray-600 leading-relaxed font-exo">
              <p>{project.description}</p>
              <p>{project.fullDescription}</p>
            </div>
          </div>
        </div>

        {/* Features */}
        {project.features && (
          <div className="mb-16 animate-on-scroll opacity-0 translate-y-8 transition-all duration-1000 ease-out delay-1000">
            <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center header-stylish font-sinkin">
              <span className="text-primary-part">Key</span> <span className="text-brand-part">Features</span>
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {project.features.map((feature: any, index: number) => (
                <div key={feature.title} className={`animate-on-scroll opacity-0 translate-y-8 transition-all duration-1000 ease-out delay-${1200 + (index * 200)}`}>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3 font-sinkin">{feature.title}</h3>
                  <p className="text-gray-600 leading-relaxed font-exo">{feature.description}</p>
                </div>
              ))}
            </div>
          </div>
        )}

      </div>

      {/* Project Gallery - Special Layout */}
      {project.images && project.images.length > 1 && (
        <div className="animate-on-scroll opacity-0 translate-y-8 transition-all duration-1000 ease-out delay-1400">
          {/* Gallery Title - Contained */}
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-12">
            <h2 className="text-3xl font-bold text-gray-900 text-center header-stylish font-sinkin">
              <span className="text-primary-part">Project</span> <span className="text-brand-part">Gallery</span>
            </h2>
          </div>

          {/* Gallery Images */}
          <div className="space-y-8">
            {project.images.slice(1).map((image: string, index: number) => {
              const isFullWidth = index % 3 === 0 || index % 3 === 2; // Full width for 1st and 3rd in each group of 3
              const isTwoColumn = index % 3 === 1; // Two column for 2nd in each group of 3
              const isFirstOfPair = isTwoColumn && index % 6 === 1; // First image of two-column pair
              const isSecondOfPair = isTwoColumn && index % 6 === 4; // Second image of two-column pair

              if (isFullWidth) {
                return (
                  <div key={index} className={`animate-on-scroll opacity-0 translate-y-8 transition-all duration-1000 ease-out delay-${1600 + (index * 200)}`}>
                    <Image
                      src={image || "/placeholder.svg"}
                      alt={`${project.title} - Image ${index + 2}`}
                      width={1600}
                      height={800}
                      className="w-full h-[70vh] object-cover"
                    />
                  </div>
                );
              }

              if (isTwoColumn && isFirstOfPair) {
                const nextImage = project.images[index + 2]; // Get the next image for the pair
                return (
                  <div key={`pair-${index}`} className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className={`grid md:grid-cols-2 gap-8 animate-on-scroll opacity-0 translate-y-8 transition-all duration-1000 ease-out delay-${1600 + (index * 200)}`}>
                      <Image
                        src={image || "/placeholder.svg"}
                        alt={`${project.title} - Image ${index + 2}`}
                        width={800}
                        height={600}
                        className="w-full h-[50vh] object-cover rounded-lg shadow-lg"
                      />
                      {nextImage && (
                        <Image
                          src={nextImage || "/placeholder.svg"}
                          alt={`${project.title} - Image ${index + 3}`}
                          width={800}
                          height={600}
                          className="w-full h-[50vh] object-cover rounded-lg shadow-lg"
                        />
                      )}
                    </div>
                  </div>
                );
              }

              // Skip rendering for second image of pair as it's already rendered above
              if (isSecondOfPair) {
                return null;
              }

              return null;
            })}
          </div>
        </div>
      )}
    </section>
  )
}
