import type { Metadata } from "next"
import Navbar from "@/components/navbar"
import Footer from "@/components/footer"
import WorkGrid from "@/components/work-grid"
import PageBanner from "@/components/page-banner"

export const metadata: Metadata = {
  title: "Our Work - CARVE Atelier Portfolio | Architecture Projects Lagos",
  description:
    "Explore CARVE Atelier's impressive portfolio of architectural projects in Lagos, Nigeria. View our commercial, residential, retail, and urban planning developments that showcase innovative design and excellence.",
  keywords: [
    "CARVE Atelier portfolio",
    "architecture projects Lagos",
    "commercial buildings Nigeria",
    "residential architecture Lagos",
    "retail design projects",
    "urban planning Nigeria",
    "architectural portfolio",
    "building projects Lagos",
    "design showcase Nigeria",
    "completed projects CARVE Atelier",
  ],
  openGraph: {
    title: "Our Work - CARVE Atelier Architecture Portfolio",
    description:
      "Explore our impressive portfolio of architectural projects showcasing innovative design and excellence in Lagos, Nigeria.",
    url: "https://carveatelier.com/work",
    images: [
      {
        url: "/og-work.jpg",
        width: 1200,
        height: 630,
        alt: "CARVE Atelier Portfolio",
      },
    ],
  },
  alternates: {
    canonical: "https://carveatelier.com/work",
  },
}

export default function Work() {
  return (
    <main className="min-h-screen">
      <Navbar />
      <PageBanner
        title="Our Work"
        subtitle="Explore our portfolio of architectural excellence"
        backgroundImage="/img/homepage/ROAD LEVEL.jpg"
      />
      <WorkGrid />
      <Footer />
    </main>
  )
}
