import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import ScrollToTop from "@/components/scroll-to-top"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  metadataBase: new URL('https://carveatelier.com'),
  title: "CARVE Atelier - Architecture & Design",
  description: "Crafting innovative architectural solutions that transform spaces and elevate experiences.",
  generator: 'v0.dev',
  icons: {
    icon: '/img/logo/favicon.ico',
    shortcut: '/img/logo/favicon.ico',
    apple: '/img/logo/favicon.ico',
  }
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <ScrollToTop />
        {children}
      </body>
    </html>
  )
}
