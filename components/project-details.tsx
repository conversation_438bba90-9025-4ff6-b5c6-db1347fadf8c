"use client"

import { useState, useEffect, useRef } from "react"
import Image from "next/image"
import Link from "next/link"
import { ArrowLeft, MapPin, Calendar, User, Building } from "lucide-react"
import { Button } from "@/components/ui/button"
import { motion, AnimatePresence } from "framer-motion"
import projectsData from "@/data/projects.json"
import { findProjectBySlug, generateSlug } from "@/lib/utils"

interface ProjectDetailsProps {
  projectSlug: string
}

export default function ProjectDetails({ projectSlug }: ProjectDetailsProps) {
  // Find project directly instead of using state
  const project = findProjectBySlug(projectsData, projectSlug)

  // Smooth scroll from bottom to top on page load
  useEffect(() => {
    // Start from bottom of page
    window.scrollTo(0, document.body.scrollHeight)

    // Smooth scroll to top after a brief delay
    const timer = setTimeout(() => {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      })
    }, 100)

    return () => clearTimeout(timer)
  }, [project])

  if (!project) {
    return <div className="py-20 text-center">Project not found</div>
  }

  return (
    <section className="bg-white">
      {/* Container for text content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <Link href="/work">
            <Button variant="outline" className="mb-6 font-sinkin">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Projects
            </Button>
          </Link>
        </motion.div>

        {/* Project Title */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="mb-12"
        >
          <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-4 header-stylish font-sinkin">
            <span className="text-primary-part">{project.title.split(' ').slice(0, -1).join(' ')}</span>{" "}
            <span className="text-brand-part">{project.title.split(' ').slice(-1)[0]}</span>
          </h1>
        </motion.div>
      </div>

      {/* Main Image - 90% Desktop, 100% Mobile */}
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
        className="mb-12 flex justify-center"
      >
        <Image
          src={project.image || "/placeholder.svg"}
          alt={project.title}
          width={1600}
          height={1200}
          className="w-full md:w-[90%] h-[120vh] object-cover rounded-lg shadow-lg"
        />
      </motion.div>

      {/* Container for remaining content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20">

        {/* Project Info - After Image */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
          className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12"
        >
          <div className="flex items-center space-x-3">
            <Building className="h-5 w-5 text-brand-primary" />
            <div>
              <p className="text-sm text-gray-500 font-exo">Project Type</p>
              <p className="font-semibold font-sinkin">{project.type}</p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <MapPin className="h-5 w-5 text-brand-primary" />
            <div>
              <p className="text-sm text-gray-500 font-exo">Location</p>
              <p className="font-semibold font-sinkin">{project.location}</p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <Calendar className="h-5 w-5 text-brand-primary" />
            <div>
              <p className="text-sm text-gray-500 font-exo">Status</p>
              <p className="font-semibold font-sinkin">{project.status}</p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <User className="h-5 w-5 text-brand-primary" />
            <div>
              <p className="text-sm text-gray-500 font-exo">Client</p>
              <p className="font-semibold font-sinkin">{project.client}</p>
            </div>
          </div>
        </motion.div>

        {/* Description - Justified Paragraphs */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.8 }}
          className="mb-12"
        >
          <div className="max-w-4xl mx-auto">
            <div className="space-y-6 text-lg text-gray-600 leading-relaxed font-exo text-justify">
              <p>{project.description}</p>
              <p>{project.fullDescription}</p>
            </div>
          </div>
        </motion.div>



        {/* Features as Justified Paragraphs */}
        {project.features && (
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 1.0 }}
            className="mb-16"
          >
            <div className="max-w-4xl mx-auto">
              <div className="space-y-6 text-lg text-gray-600 leading-relaxed font-exo text-justify">
                {project.features.map((feature: any, index: number) => (
                  <p key={index}>
                    {feature.description}
                  </p>
                ))}
              </div>
            </div>
          </motion.div>
        )}

      </div>

      {/* Project Gallery - Special Layout */}
      {project.images && project.images.length > 1 && (
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 1.0 }}
          className="mb-20"
        >
          {/* Gallery Images */}
          <div className="space-y-8">
            {project.images.slice(1).map((image: string, index: number) => {
              const isFullWidth = index % 3 === 0 || index % 3 === 2; // Full width for 1st and 3rd in each group of 3
              const isTwoColumn = index % 3 === 1; // Two column for 2nd in each group of 3
              const isFirstOfPair = isTwoColumn && index % 6 === 1; // First image of two-column pair
              const isSecondOfPair = isTwoColumn && index % 6 === 4; // Second image of two-column pair

              if (isFullWidth) {
                return (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 50 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.2 * index }}
                    className="flex justify-center"
                  >
                    <Image
                      src={image || "/placeholder.svg"}
                      alt={`${project.title} - Image ${index + 2}`}
                      width={1600}
                      height={800}
                      className="w-full md:w-[90%] h-[70vh] object-cover rounded-lg shadow-lg"
                    />
                  </motion.div>
                );
              }

              if (isTwoColumn && isFirstOfPair) {
                const nextImage = project.images[index + 2]; // Get the next image for the pair
                return (
                  <motion.div
                    key={`pair-${index}`}
                    initial={{ opacity: 0, y: 50 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.2 * index }}
                    className="flex justify-center"
                  >
                    <div className="w-full md:w-[90%] grid grid-cols-1 md:grid-cols-2 gap-8">
                      <Image
                        src={image || "/placeholder.svg"}
                        alt={`${project.title} - Image ${index + 2}`}
                        width={800}
                        height={600}
                        className="w-full h-[50vh] object-cover rounded-lg shadow-lg"
                      />
                      {nextImage && (
                        <Image
                          src={nextImage || "/placeholder.svg"}
                          alt={`${project.title} - Image ${index + 3}`}
                          width={800}
                          height={600}
                          className="w-full h-[50vh] object-cover rounded-lg shadow-lg"
                        />
                      )}
                    </div>
                  </motion.div>
                );
              }

              // Skip rendering for second image of pair as it's already rendered above
              if (isSecondOfPair) {
                return null;
              }

              return null;
            })}
          </div>
        </motion.div>
      )}
    </section>
  )
}
