"use client"

import { useEffect, useRef } from "react"
import ImageSlider from "./image-slider"

interface PageBannerSliderProps {
  title: string
  subtitle?: string
  backgroundImages: string[]
  breadcrumbs?: Array<{ label: string; href?: string }>
}

export default function PageBannerSlider({ 
  title, 
  subtitle, 
  backgroundImages, 
  breadcrumbs 
}: PageBannerSliderProps) {
  const bannerRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1 },
    )

    const elements = bannerRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  return (
    <section ref={bannerRef} className="relative h-[60vh] min-h-[400px] overflow-hidden">
      {/* Background Image Slider */}
      <div className="absolute inset-0 animate-on-scroll">
        <ImageSlider
          images={backgroundImages}
          autoSlide={true}
          autoSlideInterval={6000}
          showControls={false}
          showDots={false}
          className="w-full h-full"
          imageClassName="object-cover"
          alt={title}
        />
        <div className="absolute inset-0 bg-black/50" />
      </div>

      {/* Content */}
      <div className="relative h-full flex items-center justify-center">
        <div className="text-center text-white max-w-4xl px-4">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 animate-on-scroll animation-delay-200 font-sinkin">
            {title}
          </h1>

          {subtitle && (
            <p className="text-xl md:text-2xl text-gray-200 animate-on-scroll animation-delay-400 font-exo">
              {subtitle}
            </p>
          )}
        </div>
      </div>
    </section>
  )
}
