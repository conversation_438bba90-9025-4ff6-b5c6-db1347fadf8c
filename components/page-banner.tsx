"use client"

import { useEffect, useRef } from "react"
import Image from "next/image"

interface PageBannerProps {
  title: string
  subtitle?: string
  backgroundImage?: string
  breadcrumbs?: Array<{ label: string; href?: string }>
}

export default function PageBanner({ title, subtitle, backgroundImage, breadcrumbs }: PageBannerProps) {
  const bannerRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1 },
    )

    const elements = bannerRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  return (
    <section ref={bannerRef} className="relative h-[60vh] min-h-[400px] overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0 animate-on-scroll">
        <Image
          src={backgroundImage || "/placeholder.svg?height=600&width=1200"}
          alt={title}
          fill
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-black/50" />
      </div>

      {/* Content */}
      <div className="relative h-full flex items-center justify-center">
        <div className="text-center text-white max-w-4xl px-4">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 animate-on-scroll animation-delay-200 font-sinkin">
            {title}
          </h1>

          {subtitle && (
            <p className="text-xl md:text-2xl text-gray-200 animate-on-scroll animation-delay-400 font-exo">{subtitle}</p>
          )}
        </div>
      </div>
    </section>
  )
}
