import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Generate a URL-friendly slug from a title
 * @param title - The title to convert to a slug
 * @returns A URL-friendly slug
 */
export function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .trim() // Remove leading/trailing whitespace
    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
}

/**
 * Find a project by slug
 * @param projects - Array of projects
 * @param slug - The slug to search for
 * @returns The matching project or undefined
 */
export function findProjectBySlug(projects: any[], slug: string) {
  return projects.find(project => generateSlug(project.title) === slug)
}

/**
 * Find a news article by slug
 * @param articles - Array of news articles
 * @param slug - The slug to search for
 * @returns The matching article or undefined
 */
export function findNewsBySlug(articles: any[], slug: string) {
  return articles.find(article => generateSlug(article.title) === slug)
}
