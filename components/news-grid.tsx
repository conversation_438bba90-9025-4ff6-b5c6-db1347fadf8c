"use client"

import { useState, useEffect, useRef } from "react"
import Image from "next/image"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import newsData from "@/data/news.json"
import { generateSlug } from "@/lib/utils"

const POSTS_PER_PAGE = 6

export default function NewsGrid() {
  const [currentPage, setCurrentPage] = useState(1)
  const [news] = useState(newsData)
  const sectionRef = useRef<HTMLElement>(null)

  const totalPages = Math.ceil(news.length / POSTS_PER_PAGE)
  const startIndex = (currentPage - 1) * POSTS_PER_PAGE
  const endIndex = startIndex + POSTS_PER_PAGE
  const currentNews = news.slice(startIndex, endIndex)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1 },
    )

    const elements = sectionRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [currentPage])

  const goToPage = (page: number) => {
    setCurrentPage(page)
    window.scrollTo({ top: 0, behavior: "smooth" })
  }

  return (
    <section ref={sectionRef} className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* News Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {currentNews.map((article, index) => (
            <div
              key={article.id}
              className={`bg-white rounded-lg shadow-lg overflow-hidden animate-on-scroll animation-delay-${(index + 1) * 100}`}
            >
              <Image
                src={article.image || "/placeholder.svg"}
                alt={article.title}
                width={400}
                height={250}
                className="w-full h-48 object-cover"
              />
              <div className="p-6">
                <div className="text-sm text-brand-primary mb-2">{article.category}</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">{article.title}</h3>
                <p className="text-gray-600 mb-4 line-clamp-3">{article.description}</p>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500">{new Date(article.date).toLocaleDateString()}</span>
                  <Link href={`/news/${generateSlug(article.title)}`}>
                    <Button variant="outline" size="sm">
                      Read More
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-center space-x-2 animate-on-scroll">
            <Button variant="outline" onClick={() => goToPage(currentPage - 1)} disabled={currentPage === 1}>
              Previous
            </Button>
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <Button
                key={page}
                variant={currentPage === page ? "default" : "outline"}
                onClick={() => goToPage(page)}
                className={
                  currentPage === page
                    ? "bg-brand-primary hover:bg-brand-accent text-white"
                    : "hover:text-brand-primary hover:border-brand-primary"
                }
              >
                {page}
              </Button>
            ))}
            <Button variant="outline" onClick={() => goToPage(currentPage + 1)} disabled={currentPage === totalPages}>
              Next
            </Button>
          </div>
        )}
      </div>
    </section>
  )
}
