# CARVE Atelier Website

A modern, responsive website for CARVE Atelier - an architectural firm showcasing their portfolio, services, and expertise in Lagos, Nigeria.

## 🏗️ Features

- **Modern Design**: Clean, professional layout with smooth animations and scroll effects
- **Responsive**: Fully responsive design optimized for all devices and screen sizes
- **Real Portfolio**: 10 authentic CARVE Atelier projects with professional photography
- **SEO-Friendly URLs**: Slug-based URLs for better search engine optimization
- **Dynamic Content**: Project filtering, load more functionality, and interactive galleries
- **News & Media**: Latest updates and architectural insights with dynamic routing
- **Contact Integration**: Easy-to-use contact forms and comprehensive information
- **Performance Optimized**: Git LFS for media files, Next.js 15 optimization

## 🎯 Portfolio Highlights

### Commercial Projects (5)
- **22 ASSOCIATION** - Modern office building in Ilupeju, Lagos (Completed 2016)
- **57 ISAAC JOHN** - Contemporary workspace in Yaba, Lagos (Completed 2023)
- **OJODU WHITE TOWER** - Mixed-use development in Ogun State (Completed 2024)
- **THE STABLE** - Automotive-themed office in Lekki, Lagos (Under Construction Q3 2025)
- **LHP COMMERCIAL BUILDING** - Office development in Lagos (Design Stage)

### Retail Projects (2)
- **JARA MALL** - 5,000 sqm retail complex in Ikeja, Lagos (Completed 2019)
- **KM 46 SHOPPING MALL** - Transport hub on Lagos-Ibadan Expressway (Design Stage)

### Residential Projects (1)
- **MM RESIDENCE** - Luxury family home in Banana Island, Ikoyi (Design Stage)

### Masterplanning Projects (2)
- **PALMS COURT** - Hillside residential development in Ibadan CBD (Design Stage)
- **SUGARLAND** - Mixed residential estate with amenities in Ibadan (Design Stage)

## 🛠️ Tech Stack

- **Framework**: Next.js 15 (React 18)
- **Styling**: Tailwind CSS with custom animations
- **UI Components**: Custom components with Radix UI primitives
- **Icons**: Lucide React
- **Media Management**: Git LFS for large files
- **Deployment**: Vercel-ready
- **TypeScript**: Full type safety

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm or pnpm

### Installation

1. Clone the repository:
```bash
git clone https://github.com/Emmzy-Tech/carve-atelier-new.git
cd carve-atelier-new
```

2. Install dependencies:
```bash
npm install
# or
pnpm install
```

3. Run the development server:
```bash
npm run dev
# or
pnpm dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Project Structure

```
├── app/                    # Next.js app directory
│   ├── about/             # About page
│   ├── contact/           # Contact page
│   ├── news/              # News pages
│   ├── work/              # Work/Portfolio pages
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/            # React components
│   ├── ui/               # shadcn/ui components
│   └── ...               # Custom components
├── data/                 # Static data files
├── hooks/                # Custom React hooks
├── lib/                  # Utility functions
├── public/               # Static assets
└── styles/               # Additional styles
```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is private and proprietary to Carve Atelier.

## Contact

For any questions or support, please contact the development team.
