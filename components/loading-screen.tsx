"use client"

import { useState, useEffect } from "react"
import Image from "next/image"

interface LoadingScreenProps {
  onComplete: () => void
}

export default function LoadingScreen({ onComplete }: LoadingScreenProps) {
  const [progress, setProgress] = useState(0)
  const [currentText, setCurrentText] = useState(0)
  const [isExiting, setIsExiting] = useState(false)

  const loadingTexts = [
    "Crafting Architectural Excellence",
    "Designing Your Vision",
    "Building Tomorrow's Spaces",
    "Welcome to CARVE Atelier"
  ]

  useEffect(() => {
    // Progress animation
    const progressInterval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(progressInterval)
          // Start exit animation
          setTimeout(() => {
            setIsExiting(true)
            // Complete loading after exit animation
            setTimeout(() => {
              onComplete()
            }, 800)
          }, 500)
          return 100
        }
        return prev + 2
      })
    }, 50)

    // Text rotation
    const textInterval = setInterval(() => {
      setCurrentText(prev => (prev + 1) % loadingTexts.length)
    }, 1200)

    return () => {
      clearInterval(progressInterval)
      clearInterval(textInterval)
    }
  }, [onComplete])

  return (
    <div className={`fixed inset-0 z-50 bg-gradient-to-br from-white via-gray-50 to-gray-100 flex items-center justify-center transition-all duration-800 ${isExiting ? 'opacity-0 scale-110' : 'opacity-100 scale-100'}`}>
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-brand-primary/10 rounded-full animate-float"></div>
        <div className="absolute top-3/4 right-1/4 w-24 h-24 bg-brand-secondary/10 rounded-full animate-float-delayed"></div>
        <div className="absolute bottom-1/4 left-1/3 w-20 h-20 bg-brand-primary/5 rounded-full animate-pulse"></div>
      </div>

      <div className="relative z-10 text-center">
        {/* Logo Animation */}
        <div className="mb-12 relative">
          <div className="absolute inset-0 bg-gradient-to-r from-brand-primary/20 to-brand-secondary/20 rounded-full blur-xl animate-pulse"></div>
          <div className="relative transform transition-all duration-1000 hover:scale-105">
            <Image
              src="/img/logo/logo-light.png"
              alt="CARVE Atelier"
              width={300}
              height={120}
              className="mx-auto h-24 w-auto animate-fade-in-up"
              priority
            />
          </div>
        </div>

        {/* Animated Text */}
        <div className="mb-12 h-16 flex items-center justify-center">
          <h2 className="text-2xl md:text-3xl font-light text-gray-800 animate-fade-in-up animation-delay-500">
            {loadingTexts[currentText]}
          </h2>
        </div>

        {/* Progress Bar */}
        <div className="w-80 max-w-sm mx-auto mb-8 animate-fade-in-up animation-delay-700">
          <div className="relative">
            <div className="h-1 bg-gray-200 rounded-full overflow-hidden">
              <div 
                className="h-full bg-gradient-to-r from-brand-primary to-brand-secondary rounded-full transition-all duration-300 ease-out relative"
                style={{ width: `${progress}%` }}
              >
                <div className="absolute inset-0 bg-white/30 animate-shimmer"></div>
              </div>
            </div>
            <div className="text-sm text-gray-500 mt-2 font-medium">
              {progress}%
            </div>
          </div>
        </div>

        {/* Architectural Elements */}
        <div className="flex justify-center space-x-8 opacity-30 animate-fade-in-up animation-delay-1000">
          <div className="w-1 h-16 bg-brand-primary/40 animate-grow-height"></div>
          <div className="w-1 h-12 bg-brand-secondary/40 animate-grow-height animation-delay-200"></div>
          <div className="w-1 h-20 bg-brand-primary/40 animate-grow-height animation-delay-400"></div>
          <div className="w-1 h-8 bg-brand-secondary/40 animate-grow-height animation-delay-600"></div>
          <div className="w-1 h-14 bg-brand-primary/40 animate-grow-height animation-delay-800"></div>
        </div>

        {/* Tagline */}
        <div className="mt-8 animate-fade-in-up animation-delay-1200">
          <p className="text-sm text-gray-500 font-light tracking-wider uppercase">
            Architectural Excellence Since 2010
          </p>
        </div>
      </div>
    </div>
  )
}
