# URL Slug Mappings

This document shows the mapping between old numeric IDs and new SEO-friendly slugs.

## Work/Projects URLs

| ID | New URL | Title | Category | Status |
|----|---------|-------|----------|--------|
| 1 | `/work/22-association` | 22 ASSOCIATION | Commercial | Completed (2016) |
| 2 | `/work/jara-mall` | JARA MALL | Retail | Completed (2019) |
| 3 | `/work/palms-court` | PALMS COURT | Residential | Design Development Stage |
| 4 | `/work/sugarland` | SUGARLAND | Urban Design & Masterplan | Design Development Stage |
| 5 | `/work/57-isaac-john` | 57 ISAAC JOHN | Commercial | Completed (2023) |
| 6 | `/work/km-46-shopping-mall-bus-terminal` | KM 46 SHOPPING MALL & BUS TERMINAL | Retail | Design Development Stage |
| 7 | `/work/ojodu-white-tower` | OJODU WHITE TOWER | Commercial | Completed (2024) |
| 8 | `/work/the-stable` | THE STABLE | Commercial | Under Construction (Q3 2025) |
| 9 | `/work/mm-residence` | MM RESIDENCE | Residential | Design Development Stage |
| 10 | `/work/lhp-commercial-building` | LHP COMMERCIAL BUILDING | Commercial | Design Development Stage |

## News/Articles URLs

| Old URL | New URL | Title |
|---------|---------|-------|
| `/news/1` | `/news/carve-atelier-wins-international-architecture-award` | CARVE Atelier Wins International Architecture Award |
| `/news/2` | `/news/sustainable-design-the-future-of-architecture` | Sustainable Design: The Future of Architecture |
| `/news/3` | `/news/new-project-launch-urban-masterplan-in-abuja` | New Project Launch: Urban Masterplan in Abuja |
| `/news/4` | `/news/the-art-of-minimalist-architecture` | The Art of Minimalist Architecture |
| `/news/5` | `/news/technology-integration-in-modern-architecture` | Technology Integration in Modern Architecture |
| `/news/6` | `/news/community-engagement-in-architectural-design` | Community Engagement in Architectural Design |

## Slug Generation Rules

The `generateSlug()` function converts titles to URL-friendly slugs by:

1. Converting to lowercase
2. Removing special characters (except spaces and hyphens)
3. Replacing spaces with hyphens
4. Removing multiple consecutive hyphens
5. Trimming leading/trailing whitespace and hyphens

## Examples

- "22 ASSOCIATION" → "22-association"
- "JARA MALL" → "jara-mall"
- "OJODU WHITE TOWER" → "ojodu-white-tower"
- "THE STABLE" → "the-stable"
- "MM RESIDENCE" → "mm-residence"
- "KM 46 SHOPPING MALL & BUS TERMINAL" → "km-46-shopping-mall-bus-terminal"
- "CARVE Atelier Wins International Architecture Award" → "carve-atelier-wins-international-architecture-award"
- "Sustainable Design: The Future of Architecture" → "sustainable-design-the-future-of-architecture"

## Benefits

✅ **SEO Friendly**: Descriptive URLs improve search engine rankings
✅ **User Friendly**: URLs are readable and meaningful
✅ **Shareable**: Clean URLs are easier to share and remember
✅ **Professional**: Looks more professional than numeric IDs
✅ **Accessible**: Screen readers can better understand the content
