"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import Image from "next/image"

const slides = [
  {
    image: "/img/homepage/GHQ - COMPLETED IMAGE.jpeg",
    title: "We understand people and place.",
    subtitle: "Creating architectural solutions that resonate with communities and environments",
  },
  {
    image: "/img/homepage/JARA PROJECT PICTURE - 3.jpg",
    title: "We craft spaces that inspire.",
    subtitle: "Transforming visions into architectural masterpieces that elevate human experience",
  },
  {
    image: "/img/homepage/MM RESIDENCE - CGI - 03.jpg",
    title: "We design for tomorrow.",
    subtitle: "Building sustainable futures through innovative architectural excellence",
  },
]

export default function HeroSection() {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isTransitioning, setIsTransitioning] = useState(false)

  useEffect(() => {
    const timer = setInterval(() => {
      if (!isTransitioning) {
        setIsTransitioning(true)
        setTimeout(() => {
          setCurrentSlide((prev) => (prev + 1) % slides.length)
          setIsTransitioning(false)
        }, 300)
      }
    }, 6000)
    return () => clearInterval(timer)
  }, [isTransitioning])

  return (
    <section className="relative h-screen overflow-hidden">
      <AnimatePresence mode="wait">
        <motion.div
          key={currentSlide}
          className="absolute inset-0"
          initial={{ opacity: 0, scale: 1.1 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          transition={{ duration: 1, ease: "easeInOut" }}
        >
          <Image
            src={slides[currentSlide].image || "/placeholder.svg"}
            alt={slides[currentSlide].title}
            fill
            className="object-cover"
            priority={currentSlide === 0}
            quality={100}
          />
          <div className="absolute inset-0 bg-gradient-to-r from-black/50 via-black/30 to-black/50" />
        </motion.div>
      </AnimatePresence>

      <div className="absolute inset-0 flex items-center justify-center z-10">
        <div className="text-center text-white max-w-5xl px-4">
          <AnimatePresence mode="wait">
            <motion.h1
              key={`title-${currentSlide}`}
              className="text-4xl md:text-6xl lg:text-7xl font-light mb-6 leading-tight font-sinkin"
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -50 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              {slides[currentSlide].title}
            </motion.h1>
          </AnimatePresence>
          <AnimatePresence mode="wait">
            <motion.p
              key={`subtitle-${currentSlide}`}
              className="text-lg md:text-xl lg:text-2xl text-gray-200 font-light max-w-4xl mx-auto leading-relaxed font-exo"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -30 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              {slides[currentSlide].subtitle}
            </motion.p>
          </AnimatePresence>
        </div>
      </div>

      {/* Slide Indicators */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-3 z-20">
        {slides.map((_, index) => (
          <motion.div
            key={index}
            className={`w-3 h-3 rounded-full cursor-pointer ${
              index === currentSlide ? "bg-white" : "bg-white/50"
            }`}
            whileHover={{ scale: 1.2 }}
            animate={{ scale: index === currentSlide ? 1.25 : 1 }}
            transition={{ duration: 0.3 }}
            onClick={() => setCurrentSlide(index)}
          />
        ))}
      </div>
    </section>
  )
}
