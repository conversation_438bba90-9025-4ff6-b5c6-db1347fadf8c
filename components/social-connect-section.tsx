"use client"

import { motion } from "framer-motion"
import { Youtube, Instagram, Linkedin, Facebook } from "lucide-react"
import Image from "next/image"

const socialPlatforms = [
  {
    name: "YouTube",
    icon: Youtube,
    color: "bg-red-600",
    hoverColor: "hover:bg-red-700",
    url: "https://youtube.com/@carveatelier",
    description: "Watch our latest architectural projects and behind-the-scenes content",
    mockupImage: "/img/social/youtube-mockup.jpg"
  },
  {
    name: "Instagram",
    icon: Instagram,
    color: "bg-gradient-to-r from-purple-500 to-pink-500",
    hoverColor: "hover:from-purple-600 hover:to-pink-600",
    url: "https://instagram.com/carveatelier",
    description: "Follow our daily design inspiration and project updates",
    mockupImage: "/img/social/instagram-mockup.jpg"
  },
  {
    name: "LinkedIn",
    icon: Linkedin,
    color: "bg-blue-600",
    hoverColor: "hover:bg-blue-700",
    url: "https://linkedin.com/company/carveatelier",
    description: "Connect with our professional network and career opportunities",
    mockupImage: "/img/social/linkedin-mockup.jpg"
  },
  {
    name: "Facebook",
    icon: Facebook,
    color: "bg-blue-500",
    hoverColor: "hover:bg-blue-600",
    url: "https://facebook.com/carveatelier",
    description: "Join our community and stay updated with our latest news",
    mockupImage: "/img/social/facebook-mockup.jpg"
  }
]

export default function SocialConnectSection() {
  return (
    <section className="py-20 bg-gradient-to-br from-brand-light to-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl font-bold text-gray-900 mb-6 header-stylish font-sinkin">
            <span className="text-primary-part">Let's</span> <span className="text-brand-part">Connect</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto font-exo">
            Follow us on social media to stay updated with our latest projects, design insights, and architectural innovations
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {socialPlatforms.map((platform, index) => {
            const IconComponent = platform.icon
            return (
              <motion.div
                key={platform.name}
                className="group"
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -10 }}
              >
                <div className="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden">
                  {/* Social Platform Header */}
                  <div className={`${platform.color} ${platform.hoverColor} p-6 text-white transition-all duration-300`}>
                    <div className="flex items-center justify-center mb-4">
                      <motion.div
                        whileHover={{ scale: 1.2, rotate: 360 }}
                        transition={{ duration: 0.5 }}
                      >
                        <IconComponent className="h-12 w-12" />
                      </motion.div>
                    </div>
                    <h3 className="text-2xl font-bold text-center font-sinkin">{platform.name}</h3>
                  </div>

                  {/* Mockup Image */}
                  <div className="relative h-48 overflow-hidden">
                    <motion.div
                      className="absolute inset-0"
                      whileHover={{ scale: 1.1 }}
                      transition={{ duration: 0.3 }}
                    >
                      <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                        <div className="text-center p-4">
                          <IconComponent className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                          <p className="text-gray-500 text-sm font-exo">
                            {platform.name} Content Preview
                          </p>
                        </div>
                      </div>
                    </motion.div>
                  </div>

                  {/* Content */}
                  <div className="p-6">
                    <p className="text-gray-600 text-sm mb-6 font-exo leading-relaxed">
                      {platform.description}
                    </p>
                    
                    <motion.a
                      href={platform.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={`block w-full ${platform.color} ${platform.hoverColor} text-white text-center py-3 px-4 rounded-lg font-medium transition-all duration-300 font-exo`}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      Follow Us
                    </motion.a>
                  </div>
                </div>
              </motion.div>
            )
          })}
        </div>

        {/* Call to Action */}
        <motion.div 
          className="text-center mt-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <div className="bg-white rounded-2xl shadow-lg p-8 max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-900 mb-4 font-sinkin">
              Stay Connected with CARVE Atelier
            </h3>
            <p className="text-gray-600 mb-6 font-exo">
              Join our growing community of architecture enthusiasts, clients, and industry professionals. 
              Get exclusive insights into our design process and be the first to see our latest projects.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              {socialPlatforms.map((platform) => {
                const IconComponent = platform.icon
                return (
                  <motion.a
                    key={platform.name}
                    href={platform.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={`${platform.color} ${platform.hoverColor} text-white p-3 rounded-full transition-all duration-300`}
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <IconComponent className="h-6 w-6" />
                  </motion.a>
                )
              })}
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
