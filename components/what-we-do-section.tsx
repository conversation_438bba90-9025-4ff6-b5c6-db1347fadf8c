"use client"

import { useEffect, useRef } from "react"
import { motion } from "framer-motion"
import { Building2, ShoppingBag, Home, MapPin } from "lucide-react"

const services = [
  {
    title: "Commercial",
    icon: Building2,
    description:
      "We excel in creating dynamic and functional commercial spaces tailored to our clients' business needs. Our expertise in commercial design includes innovative solutions that drive business success.",
  },
  {
    title: "Retail Design",
    icon: ShoppingBag,
    description:
      "Transforming retail environments into captivating and customer-centric spaces is our specialty. We create immersive shopping experiences that connect brands with their customers.",
  },
  {
    title: "Residential",
    icon: Home,
    description:
      "We design homes that resonate with the lifestyles and aspirations of our clients. Each residential project reflects the unique personality and needs of those who will call it home.",
  },
  {
    title: "Master Planning & Urban Design",
    icon: MapPin,
    description:
      "We specialize in shaping communities through thoughtful master planning and urban design strategies that create sustainable, livable environments for future generations.",
  },
]

export default function WhatWeDoSection() {
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1 },
    )

    const elements = sectionRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  return (
    <section ref={sectionRef} className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl font-bold text-gray-900 mb-6 header-stylish">
            <span className="text-primary-part">Solutions we</span> <span className="text-brand-part">deliver</span>
          </h2>
          <p className="text-lg text-gray-600 max-w-4xl mx-auto leading-relaxed font-exo">
            CARVE Atelier is an Architecture and Design firm specialized in crafting Innovative and practical solutions
            to projects, with a primary focus on fulfilling our client goals. Fueled by an deep-seated passion for
            design excellence, our approach is driven by principles aimed at transforming existing sites in a unique
            ways that address programmatic and operational needs. We provide professional services that not only meet
            but exceed our clients' expectations, delivering substantial value.
          </p>
        </motion.div>
        <div className="grid md:grid-cols-2 gap-8">
          {services.map((service, index) => {
            const IconComponent = service.icon
            return (
              <motion.div
                key={service.title}
                className="bg-gradient-to-br from-brand-light to-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300"
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5, scale: 1.02 }}
              >
                <motion.div
                  className="flex items-center mb-4"
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                >
                  <motion.div
                    className="bg-brand-primary p-3 rounded-full mr-4"
                    whileHover={{ rotate: 360 }}
                    transition={{ duration: 0.5 }}
                  >
                    <IconComponent className="h-6 w-6 text-white" />
                  </motion.div>
                  <h3 className="text-2xl font-semibold text-gray-900 font-sinkin">{service.title}</h3>
                </motion.div>
                <motion.p
                  className="text-gray-600 leading-relaxed font-exo"
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                >
                  {service.description}
                </motion.p>
              </motion.div>
            )
          })}
        </div>
      </div>
    </section>
  )
}
