"use client"

import { useEffect, useRef } from "react"
import Link from "next/link"
import Image from "next/image"
import { Mail, Phone, MapPin } from "lucide-react"

export default function Footer() {
  const footerRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1 },
    )

    const elements = footerRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  return (
    <footer ref={footerRef} className="bg-gray-900 text-white py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div className="animate-on-scroll">
            <Image
              src="/img/logo/logo-dark-bg.png"
              alt="CARVE ATELIER"
              width={180}
              height={48}
              className="h-12 w-auto mb-4 animate-on-scroll"
            />
            <p className="text-gray-300 mb-4 animate-on-scroll animation-delay-100">
              Crafting innovative architectural solutions that transform spaces and elevate experiences.
            </p>
          </div>
          <div className="animate-on-scroll animation-delay-100">
            <h4 className="text-lg font-semibold mb-4 animate-on-scroll animation-delay-150">Quick Links</h4>
            <ul className="space-y-2">
              <li className="animate-on-scroll animation-delay-200">
                <Link href="/" className="text-gray-300 hover:text-brand-secondary transition-colors">
                  Home
                </Link>
              </li>
              <li className="animate-on-scroll animation-delay-250">
                <Link href="/about" className="text-gray-300 hover:text-brand-secondary transition-colors">
                  About Us
                </Link>
              </li>
              <li className="animate-on-scroll animation-delay-300">
                <Link href="/work" className="text-gray-300 hover:text-brand-secondary transition-colors">
                  Our Work
                </Link>
              </li>
              <li className="animate-on-scroll animation-delay-350">
                <Link href="/news" className="text-gray-300 hover:text-brand-secondary transition-colors">
                  News & Media
                </Link>
              </li>
              <li className="animate-on-scroll animation-delay-400">
                <Link href="/contact" className="text-gray-300 hover:text-brand-secondary transition-colors">
                  Contact Us
                </Link>
              </li>
            </ul>
          </div>
          <div className="animate-on-scroll animation-delay-200">
            <h4 className="text-lg font-semibold mb-4 animate-on-scroll animation-delay-250">Services</h4>
            <ul className="space-y-2 text-gray-300">
              <li className="animate-on-scroll animation-delay-300">Master Planning & Urban Design</li>
              <li className="animate-on-scroll animation-delay-350">Retail Design</li>
              <li className="animate-on-scroll animation-delay-400">Commercial & Civic Design</li>
              <li className="animate-on-scroll animation-delay-450">Residential Design</li>
            </ul>
          </div>
          <div className="animate-on-scroll animation-delay-300">
            <h4 className="text-lg font-semibold mb-4 animate-on-scroll animation-delay-350">Contact Info</h4>
            <div className="space-y-3">
              <div className="flex items-center space-x-3 animate-on-scroll animation-delay-400">
                <MapPin className="h-5 w-5 text-brand-secondary" />
                <span className="text-gray-300 text-sm">No 57, Isaac John Street, Yaba, Lagos</span>
              </div>
              <div className="flex items-center space-x-3 animate-on-scroll animation-delay-450">
                <Phone className="h-5 w-5 text-brand-secondary" />
                <span className="text-gray-300 text-sm">+234 9064525110</span>
              </div>
              <div className="flex items-center space-x-3 animate-on-scroll animation-delay-500">
                <Mail className="h-5 w-5 text-brand-secondary" />
                <span className="text-gray-300 text-sm"><EMAIL></span>
              </div>
            </div>
          </div>
        </div>
        <div className="border-t border-gray-800 mt-8 pt-8 text-center animate-on-scroll animation-delay-600">
          <p className="text-gray-400">&copy; {new Date().getFullYear()} CARVE Atelier. All rights reserved.</p>
        </div>
      </div>
    </footer>
  )
}
